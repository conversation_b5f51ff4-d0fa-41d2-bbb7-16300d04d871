# Expansion Scope Outline

This document captures all content dimensions we’ll need to plan and scope for future expansions, modular additions, and balancing.

---

## 1. Building Categories & Types

### 1.1 Defensive Structures
- **Turrets:** Basic Cannon, Rapid‑Fire Gun, Missile Launcher, Laser Tower
- **Area Effects:** Shield Generator, Decoy Emitter, <PERSON><PERSON> Blaster
- **Traps:** Spike Minefield, Gravity Well, Slow Field Projector

### 1.2 Utility & Support Buildings
- **Resource Harvesters:** Scrap Collector, Gem Extractor
- **Production:** Ammo Depot, Drone Factory
- **Research Facilities:** Tech Lab, Archive, Artifact Workshop
- **Housing:** Residential Block, High‑Rise Apartments (population cap growth)

### 1.3 Special & Event Buildings
- **Seasonal Modules:** Ice Beacon, Volcanic Forge
- **Boss‑Specific Platforms:** Omega Conduit, Rift Stabilizer

---

## 2. Skill Tree Branches & Skills

### 2.1 Branches
- **Offense:** Boost damage, critical chance, projectile speed
- **Defense:** Increase turret HP, shield capacity, decoy duration
- **Support:** Resource gains, research speed, ability cooldown reduction
- **Artifact Mastery:** Enhance passive Artifact effects, stacking bonuses

### 2.2 Example Skill <PERSON>des
| Node ID        | Branch    | Cost | Prerequisites    | Effects                                   | Rarity Unlock |
|----------------|-----------|------|------------------|-------------------------------------------|--------------|
| RapidFire      | Offense   | 2    | BasicTurrets     | -20% reloadTime                           | Common       |
| ReinforcedHull | Defense   | 3    | ShieldGenerator  | +15% building maxHealth                  | Uncommon     |
| TechSavant     | Support   | 4    | TechLabLevel2    | +10% researchRate                        | Rare         |
| ArtifactBond   | Artifact  | 5    | “N/A”    | +1% all Artifact effect potency per rank | Unique       |

---

## 3. Weapon Types & Variations

### 3.1 Categories
- **Ballistic:** Cannons, Machine Gun, Mortar
- **Explosive:** Rocket Pods, Missile Batteries, Cluster Launchers
- **Energy:** Laser Array, Plasma Cannon, Ion Beam
- **Special:** Orbital Strike Beacon, Temporal Distorter

### 3.2 Variation Attributes
- **Damage:** 50–1000
- **Rate of Fire:** 0.1–2.0 shots/sec
- **Range:** 200–1000 units
- **Critical Chance:** 0–120%
- **Special Properties:** Splash Radius, Piercing, Homing


### 3.3 Building Associations
| Building           | Supported Weapons            |
|--------------------|------------------------------|
| Basic Turret Base  | Cannon, Machine Gun          |
| Missile Platform   | Rocket Pods, Cluster Launcher|
| Energy Relay Node  | Laser Array, Ion Beam        |

---

## 4. Item & Artifact Categories

### 4.1 Rarity Tiers & Attribute Slots
- **Normal:** 1–2 attributes
- **Uncommon:** 2–3 attributes
- **Rare:** 3–4 attributes
- **Unique:** 4+ attributes + special effect

### 4.2 Attribute Types & Ranges
| Attribute         | Min Value | Max Value | Notes                          |
|-------------------|-----------|-----------|--------------------------------|
| +Damage%          | 5%        | 50%       | Applies multiplicatively       |
| +Fire Rate%       | 5%        | 40%       | Affects all turrets            |
| +Resource Gain%   | 2%        | 30%       | Credits per wave increment     |
| +Population Rate% | 5%        | 25%       | Citizens rescued per wave      |
| Cooldown Reduction| 5%        | 20%       | Affects active abilities       |

### 4.3 Sources & Acquisition
- **In‑Run Finds:** Chests, enemy drops, boss rewards
- **Research Unlocks:** Lab discoveries unlock blueprints, recipes
- **Meta‑Shop (future):** Use meta‑currency to purchase or reroll items

---

## 5. Findable In‑Game vs. Research/Meta

| Content Type        | In‑Game Acquisition            | Research/Meta Unlock          |
|---------------------|--------------------------------|-------------------------------|
| Buildings           | Pick‑3 between waves           | Unlock new structures permanently via Skill Tree   |
| Weapons             | Loot drops, chests             | Blueprints in Tech Lab        |
| Items/Artifacts     | Boss drops, rare crates        | Artifact Workshop investigations   |
| Skill Nodes         | N/A (spent meta‑currency)      | Meta XP & Skill Tree progression|

---

*This outline will guide our backlog scoping, content pipelines, and data‑schema design for each module.*

