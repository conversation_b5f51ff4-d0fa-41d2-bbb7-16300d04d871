# MVP Implementation TODOs

This TODO document outlines actionable tasks for our development teams to kick off the MVP of **Missile Defender: Roguelike RPG Edition** using stand‑in graphics and workflows.

---

## 1. Core Game Loop & Simulation

- **1.1 Placement Stage**  
  - Implement UI shell for between‑wave placement panel (React placeholder).  
  - Hook up currency display (unified Credits only).  
  - Basic building placement interaction on canvas grid.

- **1.2 Battle Stage (Auto‑Battler)**  
  - Create mock projectile and enemy sprites (placeholder shapes).  
  - Implement procedural wave spawner (configurable JSON).  
  - Simulate turret targeting logic: simple nearest‑threat targeting.

- **1.3 Survival & Loop Transition**  
  - Detect end‑of‑wave conditions (all enemies dead or city HP ≤ 0).  
  - Transition back to placement UI or run end screen.

- **1.4 Pick‑3 Research Interlude**  
  - Stub out modal UI for random 3 choices (buildings/upgrades).  
  - Random selection logic from unlockable pool (initial small set).

---

## 2. Data & Config Schema

- **2.1 JSON Schema Definitions**  
  - Define `BuildingConfig.json` schema (id, cost, range, sprite, components).  
  - Define `EnemyConfig.json` schema (id, health, speed, lootTable).  
  - Define `WeaponConfig.json` schema (id, damage, ROF, range, special).  
  - Define `SkillNodeConfig.json` schema (id, branch, cost, effects).

- **2.2 Config Loader**  
  - Implement loader module to read JSON configs into engine registry.  
  - Validate one sample building and one enemy.

---

## 3. Frontend Prototype (React + Canvas)

- **3.1 Project Setup**  
  - Initialize React + TypeScript project.  
  - Integrate Canvas rendering library (PixiJS/Phaser stub).

- **3.2 HUD & Overlays**  
  - Credits counter, wave timer, health bar.  
  - Basic skill‑tree access button (non‑functional stub).

- **3.3 Canvas Interaction**  
  - Render grid/planet ring segment.  
  - Click to place placeholder building icons.

---

## 4. Backend & Server Simulation PoC

- **4.1 Node.js Server**  
  - Set up Express server with WebSocket endpoint.  
  - Authoritative tick loop at lower frequency (e.g., 10 Hz).

- **4.2 Hybrid Simulation**  
  - Server: manage game state for one session with simple projectile physics.  
  - Client: subscribe to state frames, render predicted local movement.

- **4.3 Cheat Prevention Stub**  
  - Enforce simple spend checks on building placement.  
  - Reject invalid placement requests.

---

## 5. Infrastructure & Tooling

- **5.1 Repository Setup**  
  - Create mono-repo with `frontend/`, `backend/`, `data/` directories.  
  - Configure linting, formatting, and CI pipeline (GitHub Actions stub).

- **5.2 Environment & Deployment**  
  - Dockerize both front-end and back-end for local development.  
  - Write `docker-compose.yml` for easy spin-up.

---

## 6. Testing & Validation

- **6.1 Unit Tests**  
  - Schema validation tests for JSON configs.  
  - Utility functions for random pick logic.

- **6.2 Integration Tests**  
  - Simulate a single wave: placement → battle → loop transition.  
  - Verify resource accounting (enemy credits, population credits).

- **6.3 Performance Smoke Tests**  
  - Render 100+ projectiles on canvas; measure frame rate.

---

*Assign tasks to sprints and track progress in JIRA/Trello.*

